const { includes, get } = require('lodash');
const Project = require('../models/project');
const Submission = require('../models/Submission');
const Callout = require('../models/callout');
const ProjectSnap = require('../models/projectSnaps');
const AuthService = require('../services/AuthService');
const SubmissionService = require('../services/SubmissionService');
const MailService = require('../services/MailService');

const { getCreatorFormateObject } = require('../utils/helpers');

// Custom API Error class
class ApiError extends Error {
  constructor(message, statusCode = 500, error = 'internal') {
    super(message);
    this.statusCode = statusCode;
    this.error = error;
  }
}

class SubmissionController {
  /**
   * Create a new submission
   * @param {Object} request - Hapi request object
   * @param {Object} h - Hapi response toolkit
   * @returns {Object} Hapi response
   */
  static async create(request, h) {
    try {
      request.logger &&
        request.logger.info('SubmissionController.create called');
      const { calloutId, snapshotId } = request.payload;
      const user = request.user;

      // Check if a submission with the same snapshotId and calloutId already exists (do this first)
      const existingSubmission = await Submission.findOne({
        snapshotId,
        calloutId,
      });
      if (existingSubmission) {
        throw new ApiError(
          'Snapshot is already submitted in this callout',
          409,
          'conflict',
        );
      }

      // Check existence of snapshot
      const snapshot = await ProjectSnap.findById(snapshotId).select(
        'projectId body creator',
      );
      if (!snapshot) {
        throw new ApiError('Snapshot does not exist!', 404, 'not_found');
      }

      // Check existence of project
      const project = await Project.findById(snapshot.projectId).select(
        'creator',
      );
      if (!project) {
        throw new ApiError('Project does not exist!', 404, 'not_found');
      }
      // Check existence of callout
      const callout = await Callout.findById(calloutId).select(
        '_id body.companyName name',
      );
      if (!callout) {
        throw new ApiError('Callout does not exist!', 404, 'not_found');
      }
      // Prepare submittedBy with role
      const submittedBy = getCreatorFormateObject(user);
      const isAdmin = await AuthService.isRoleExist(request, 'admin');
      if (isAdmin) {
        submittedBy.role = 'cupid';
      } else if (
        project.creator &&
        project.creator.userId &&
        String(project.creator.userId) !== String(user._id)
      ) {
        submittedBy.role = 'collaborator';
      } else {
        submittedBy.role = 'user';
      }

      // Map snapshot.creator.profileImage to avatar for snapshotCreator
      let snapshotCreator = snapshot.creator;
      if (
        snapshotCreator &&
        snapshotCreator.profileImage &&
        snapshotCreator.fullName
      ) {
        snapshotCreator = {
          ...snapshotCreator,
          avatar: snapshotCreator.profileImage,
          username: snapshotCreator.fullName,
        };
        delete snapshotCreator.profileImage;
        delete snapshotCreator.fullName;
      }

      const submission = await Submission.create({
        calloutId,
        projectId: project._id,
        snapshotId,
        projectCreator: project.creator,
        snapshotCreator,
        submittedBy,
      });

      await Project.findByIdAndUpdate(project._id, {
        $inc: { totalSubmissions: 1 },
      });
      await Callout.findByIdAndUpdate(callout._id, {
        $inc: { totalSubmissions: 1 },
      });

      // Send submission received email
      try {
        const snapshotBody = snapshot.body ? JSON.parse(snapshot.body) : {};
        await MailService.sendEmail(
          'submissionReceived',
          {
            id: user._id,
            email: user.email,
          },
          {
            webBaseUrl: process.env.WEBAPP_BASE_URL,
            calloutName: callout.name || '',
            snapshotImage: get(snapshotBody, 'cover.coverPic', ''),
            title: snapshotBody.cover ? snapshotBody.cover.title : '',
            username: snapshotBody.creator ? snapshotBody.creator.username : '',
            logLine: snapshotBody.basicInfo
              ? snapshotBody.basicInfo.logLine
              : '',
            companyName: callout.body ? callout.body.companyName : '',
          },
        );
      } catch (error) {
        request.logger &&
          request.logger.error(
            error,
            'Error in SubmissionController.create send email',
          );
      }
      request.logger &&
        request.logger.info('SubmissionController.create success');
      return h
        .response({
          statusCode: 201,
          message: 'Submission created successfully.',
          data: submission,
        })
        .code(201);
    } catch (error) {
      request.logger &&
        request.logger.error(error, 'Error in SubmissionController.create');
      return h
        .response({
          statusCode: error.statusCode || 500,
          message: error.message || 'Internal server error',
          error: error.error || 'internal',
        })
        .code(error.statusCode || 500);
    }
  }

  /**
   * Update submission to slate
   * @param {Object} request - Hapi request object
   * @param {Object} h - Hapi response toolkit
   * @returns {Object} Hapi response
   */
  static async updateSubmissionType(request, h) {
    try {
      request.logger &&
        request.logger.info('SubmissionController.updateSubmissionType called');
      const { id } = request.params;
      const { type } = request.payload;
      const user = request.user;
      const updatedBy = getCreatorFormateObject(user);
      const status = type === 'submission' ? 'NEW' : 'AWAITING_FEEDBACK';
      const isEmailSent = false;
      const updated = await SubmissionService.updateSubmissionType(
        id,
        updatedBy,
        type,
        status,
        isEmailSent,
      );
      request.logger &&
        request.logger.info(
          'SubmissionController.updateSubmissionType success',
        );
      return h
        .response({
          statusCode: 200,
          message: `Submission status has been updated to ${type}.`,
          data: {
            id: updated._id,
            type: updated.type,
            status: updated.status,
            slateUpdatedBy: updated.slateUpdatedBy,
            slateUpdatedAt: updated.slateUpdatedAt,
          },
        })
        .code(200);
    } catch (error) {
      request.logger &&
        request.logger.error(
          error,
          'Error in SubmissionController.updateSubmissionType',
        );
      return h
        .response({
          statusCode: error.statusCode || 500,
          message: error.message || 'Internal server error',
          error: error.error || 'internal',
        })
        .code(error.statusCode || 500);
    }
  }

  /**
   * Add feedback to a submission
   * @param {Object} request - Hapi request object
   * @param {Object} h - Hapi response toolkit
   * @returns {Object} Hapi response
   */
  static async addFeedback(request, h) {
    try {
      request.logger &&
        request.logger.info('SubmissionController.addFeedback called');
      const { id } = request.params;
      const { feedback } = request.payload;
      const user = request.user;
      const feedbackGivenBy = getCreatorFormateObject(user);
      // entity is handled in the service now
      const updated = await SubmissionService.addFeedback(
        id,
        feedback,
        feedbackGivenBy,
      );
      request.logger &&
        request.logger.info('SubmissionController.addFeedback success');

      // send email
      try {
        const submission = await Submission.findById(id).select(
          'snapshotId calloutId',
        );
        const callout = await Callout.findById(submission.calloutId).select(
          '_id name',
        );
        if (!callout) {
          throw new ApiError('Callout does not exist!', 404, 'not_found');
        }
        const snapshot = await ProjectSnap.findById(
          submission.snapshotId,
        ).select('creator hash body');
        if (!snapshot) {
          throw new ApiError('Snapshot does not exist!', 404, 'not_found');
        }
        const snapshotBody = snapshot.body ? JSON.parse(snapshot.body) : {};
        await MailService.sendEmail(
          'submissionFeedback',
          {
            id: request.user._id,
            email: snapshot.creator && snapshot.creator.email,
          },
          {
            webBaseUrl: process.env.WEBAPP_BASE_URL,
            email: snapshot.creator && snapshot.creator.email,
            calloutName: callout.name,
            calloutId: callout._id,
            feedback,
            snapshotHash: snapshot,
            snapshotImage: get(snapshotBody, 'cover.coverPic', ''),
            title: get(snapshotBody, 'cover.title', ''),
            username: get(snapshotBody, 'creator.username', ''),
            logLine: get(snapshotBody, 'basicInfo.logLine', ''),
          },
        );
      } catch (error) {
        request.logger &&
          request.logger.error(
            error,
            'Error in SubmissionController.updateTracking add feedback email',
          );
      }
      return h
        .response({
          statusCode: 200,
          message: 'Feedback added to submission.',
          data: {
            id: updated._id,
            feedback: updated.feedback,
          },
        })
        .code(200);
    } catch (error) {
      request.logger &&
        request.logger.error(
          error,
          'Error in SubmissionController.addFeedback',
        );
      return h
        .response({
          statusCode: error.statusCode || 500,
          message: error.message || 'Internal server error',
          error: error.error || 'internal',
        })
        .code(error.statusCode || 500);
    }
  }

  /**
   * Add note to a slate
   * @param {Object} request - Hapi request object
   * @param {Object} h - Hapi response toolkit
   * @returns {Object} Hapi response
   */
  static async addNote(request, h) {
    try {
      request.logger &&
        request.logger.info('SubmissionController.addNote called');
      const { id } = request.params;
      const { note } = request.payload;
      // Don't need to check if submission exist or note
      const submission = await Submission.findById(id).select('type');
      if (!submission) {
        return h
          .response({
            statusCode: 404,
            message: 'Submission not found',
            error: 'not_found',
          })
          .code(404);
      }
      if (submission.type !== 'slate') {
        return h
          .response({
            statusCode: 200,
            message: 'Note can be added to slate only',
            error: 'not_found',
          })
          .code(404);
      }
      const updated = await SubmissionService.addNote(id, note);
      request.logger &&
        request.logger.info(
          `SubmissionController.addNote success resp ${JSON.stringify(
            updated,
          )}`,
        );

      return h
        .response({
          statusCode: 200,
          message: 'Note has been added to slate.',
          data: {
            id: get(updated, '_id'),
            note: get(updated, 'note'),
          },
        })
        .code(200);
    } catch (error) {
      request.logger &&
        request.logger.error(error, 'Error in SubmissionController.addNote');
      return h
        .response({
          statusCode: error.statusCode || 500,
          message: error.message || 'Internal server error',
          error: error.error || 'internal',
        })
        .code(error.statusCode || 500);
    }
  }

  /**
   * Update submission tracking (status, isEmailSent)
   * @param {Object} request - Hapi request object
   * @param {Object} h - Hapi response toolkit
   * @returns {Object} Hapi response
   */
  static async updateTracking(request, h) {
    try {
      request.logger &&
        request.logger.info('SubmissionController.updateTracking called');
      const { id } = request.params;
      const { status, isEmailSent } = request.payload;
      if (typeof status === 'undefined' && typeof isEmailSent === 'undefined') {
        throw new ApiError(
          'At least one of status or isEmailSent must be provided',
          400,
          'bad_request',
        );
      }
      // Fetch submission to check type
      const submission = await Submission.findById(id).select(
        'type status snapshotId calloutId',
      );
      if (!submission) {
        return h
          .response({
            statusCode: 404,
            message: 'Submission not found',
            error: 'not_found',
          })
          .code(404);
      }
      // Validate status for type
      if (typeof status !== 'undefined') {
        const valid =
          require('../services/SubmissionService').isValidStatusForType(
            submission.type,
            status,
          );
        if (!valid) {
          return h
            .response({
              statusCode: 400,
              message: `Status '${status}' is not valid for type '${submission.type}'`,
              error: 'bad_request',
            })
            .code(400);
        }
      }
      // Only allow isEmailSent=true if type==='slate'
      if (isEmailSent === true && submission.type !== 'slate') {
        return h
          .response({
            statusCode: 400,
            message: `isEmailSent can only be set to true when type is 'slate'`,
            error: 'bad_request',
          })
          .code(400);
      }
      const update = {};
      if (typeof status !== 'undefined') update.status = status;
      if (typeof isEmailSent !== 'undefined') update.isEmailSent = isEmailSent;
      const updated = await Submission.findByIdAndUpdate(id, update, {
        new: true,
      });
      if (!updated) {
        request.logger &&
          request.logger.info('SubmissionController.updateTracking not found');
        return h
          .response({
            statusCode: 404,
            message: 'Submission not found',
            error: 'not_found',
          })
          .code(404);
      }
      request.logger &&
        request.logger.info('SubmissionController.updateTracking success');

      if (includes(['NOT_INTERESTED', 'REJECTED'], status)) {
        try {
          const snapshot = await ProjectSnap.findById(
            submission.snapshotId,
          ).select('creator');
          if (!snapshot) {
            throw new ApiError('Snapshot does not exist!', 404, 'not_found');
          }
          const callout = await Callout.findById(submission.calloutId).select(
            '_id name',
          );
          if (!callout) {
            throw new ApiError('Callout does not exist!', 404, 'not_found');
          }
          // const snapshotBody = snapshot.body ? JSON.parse(snapshot.body) : {};
          await MailService.sendEmail(
            'rejectSubmissionEmail',
            {
              id: request.user._id,
              email: snapshot.creator ? snapshot.creator.email : '',
            },
            {
              creatorName: snapshot.creator
                ? get(snapshot, 'creator.fullName', '')
                : '',
              calloutName: callout.name,
              webBaseUrl: process.env.WEBAPP_BASE_URL,
            },
          );
        } catch (error) {
          request.logger &&
            request.logger.error(
              error,
              'Error in SubmissionController.updateTracking notInterested send email',
            );
        }
      }
      // slate status change email
      if (submission.type === 'slate' && submission.status !== status) {
        try {
          const callout = await Callout.findById(submission.calloutId).select(
            '_id name',
          );
          if (!callout) {
            throw new ApiError('Callout does not exist!', 404, 'not_found');
          }
          const snapshot = await ProjectSnap.findById(
            submission.snapshotId,
          ).select('creator hash body');
          if (!snapshot) {
            throw new ApiError('Snapshot does not exist!', 404, 'not_found');
          }
          const snapshotBody = snapshot.body ? JSON.parse(snapshot.body) : {};
          // const snapshotBody = snapshot.body ? JSON.parse(snapshot.body) : {};
          await MailService.sendEmail(
            'slateStatusChange',
            {
              id: request.user._id,
              email: process.env.CUPID_EMAILS,
            },
            {
              webBaseUrl: process.env.WEBAPP_BASE_URL,
              opsUrl: process.env.OPS_BASE_URL,
              calloutId: callout._id,
              calloutName: callout.name,
              slateStatus: status
                .toLowerCase()
                .split('_')
                .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                .join(' '),
              snapshotHash: snapshot.hash,
              snapshotImage: get(snapshotBody, 'cover.coverPic', ''),
              title: get(snapshotBody, 'cover.title', ''),
              username: get(snapshotBody, 'creator.username', ''),
              logLine: get(snapshotBody, 'basicInfo.logLine', ''),
            },
          );
        } catch (error) {
          request.logger &&
            request.logger.error(
              error,
              'Error in SubmissionController.updateTracking slate status change email',
            );
        }
      }
      return h
        .response({
          statusCode: 200,
          message: 'Submission tracking updated successfully.',
          data: {
            id: updated._id,
            status: updated.status,
            isEmailSent: updated.isEmailSent,
          },
        })
        .code(200);
    } catch (error) {
      request.logger &&
        request.logger.error(
          error,
          'Error in SubmissionController.updateTracking',
        );
      return h
        .response({
          statusCode: error.statusCode || 500,
          message: error.message || 'Internal server error',
          error: error.error || 'internal',
        })
        .code(error.statusCode || 500);
    }
  }

  /**
   * Get submission detail
   * @param {Object} request - Hapi request object
   * @param {Object} h - Hapi response toolkit
   * @returns {Object} Hapi response
   */
  static async detail(request, h) {
    try {
      request.logger &&
        request.logger.info('SubmissionController.detail called');
      const { id } = request.params;
      const detail = await SubmissionService.getDetail(id);
      if (!detail) {
        request.logger &&
          request.logger.info('SubmissionController.detail not found');
        return h
          .response({
            statusCode: 404,
            message: 'Submission not found',
            error: 'not_found',
          })
          .code(404);
      }
      request.logger &&
        request.logger.info('SubmissionController.detail success');
      return h
        .response({
          statusCode: 200,
          message: 'Submission detail fetched successfully.',
          data: detail,
        })
        .code(200);
    } catch (error) {
      request.logger &&
        request.logger.error(error, 'Error in SubmissionController.detail');
      return h
        .response({
          statusCode: error.statusCode || 500,
          message: error.message || 'Internal server error',
          error: error.error || 'internal',
        })
        .code(error.statusCode || 500);
    }
  }

  /**
   * List submissions
   * @param {Object} request - Hapi request object
   * @param {Object} h - Hapi response toolkit
   * @returns {Object} Hapi response
   */
  static async list(request, h) {
    try {
      request.logger && request.logger.info('SubmissionController.list called');
      const query = request.parsedQuery;
      const { where, options } = query;

      if (typeof options.select === 'undefined') {
        options.select = '_id';
      }

      // Add default sorting by createdAt in descending order (newest to oldest) if no sort is specified
      if (!options.sort) {
        options.sort = { createdAt: -1 };
      }

      const result = await SubmissionService.fetch(where, options);
      request.logger &&
        request.logger.info('SubmissionController.list success');
      // Format response as per mongoose-paginate
      return h
        .response({
          statusCode: 200,
          message: 'Submissions fetched successfully.',
          data: result,
        })
        .code(200);
    } catch (error) {
      request.logger &&
        request.logger.error(error, 'Error in SubmissionController.list');
      return h
        .response({
          statusCode: error.statusCode || 500,
          message: error.message || 'Internal server error',
          error: error.error || 'internal',
        })
        .code(error.statusCode || 500);
    }
  }
}

module.exports = SubmissionController;
